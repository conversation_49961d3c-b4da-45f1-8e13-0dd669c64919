import { fabric } from "fabric";
import {
  EditorElement,
  Placement,
  TextEditorElement,
  VideoEditorElement,
  ImageEditorElement,
  AudioEditorElement,
  BorderStyle,
  ShapeEditorElement,
  ShapeType,
  TimeFrame,
} from "../types";
import { HistoryActionType } from "./HistoryManager";
import { Store } from "./Store";
import {
  getUid,
  isHtmlImageElement,
  isHtmlVideoElement,
  isHtmlAudioElement,
} from "../utils";
import {
  CoverImage,
  CoverVideo,
  TextboxWithPadding,
} from "../utils/fabric-utils";
import { isEditorImageElement, isEditorMediaElement } from "../utils";

// 常量定义
const SCALING_THRESHOLD = 0.01;
const CLONE_OFFSET = 20;
const DEFAULT_BORDER_RADIUS = 10;

// 元素创建相关常量
const ELEMENT_CONSTANTS = {
  CANVAS: {
    DEFAULT_WIDTH: 1920,
    DEFAULT_HEIGHT: 1080,
  },
  MEDIA: {
    DEFAULT_VIDEO_HEIGHT: 400,
    DEFAULT_IMAGE_SIZE: 300,
    DEFAULT_SHAPE_SIZE: 200,
    DEFAULT_LINE_WIDTH: 300,
    DEFAULT_LINE_HEIGHT: 5,
    DEFAULT_ELLIPSE_WIDTH: 250,
    DEFAULT_ELLIPSE_HEIGHT: 150,
  },
  TIMELINE: {
    DEFAULT_ELEMENT_DURATION: {
      TEXT: 5000,
      IMAGE: 3000,
      SHAPE: 5000,
    },
  },
};

// 媒体元素类型
const MEDIA_TYPES = {
  VIDEO: "video",
  AUDIO: "audio",
  IMAGE: "image",
  TEXT: "text",
  SHAPE: "shape",
} as const;

// 对齐类型映射
const ALIGNMENT_ACTIONS = {
  left: (object: fabric.Object) => object.set({ left: 0 }),
  center: (object: fabric.Object) => object.centerH(),
  right: (object: fabric.Object, canvas: fabric.Canvas) => {
    const width = object.width || 0;
    const scaleX = object.scaleX || 1;
    object.set({ left: canvas.width - width * scaleX });
  },
  top: (object: fabric.Object) => object.set({ top: 0 }),
  middle: (object: fabric.Object) => object.centerV(),
  bottom: (object: fabric.Object, canvas: fabric.Canvas) => {
    const height = object.height || 0;
    const scaleY = object.scaleY || 1;
    object.set({ top: canvas.height - height * scaleY });
  },
  justify: (object: fabric.Object) => object.center(),
} as const;

// 滤镜方法映射
const FILTER_METHOD_MAP = {
  brightness: "setBrightness",
  contrast: "setContrast",
  saturation: "setSaturation",
  hue: "setHue",
  blur: "setBlur",
} as const;

// 基础fabric属性映射
const BASIC_STYLE_UPDATES = {
  fontSize: "fontSize",
  textAlign: "textAlign",
  charSpacing: "charSpacing",
  lineHeight: "lineHeight",
  fontColor: "fill",
  fontFamily: "fontFamily",
  strokeWidth: "strokeWidth",
  strokeColor: "stroke",
} as const;

export class ElementManager {
  private store: Store;
  private canvas: fabric.Canvas;
  private cropEventHandler: ((e: fabric.IEvent<Event>) => void) | null = null;

  constructor(store: Store) {
    this.store = store;
  }
  setCanvas(canvas: fabric.Canvas) {
    this.canvas = canvas;
  }

  // ===== 辅助方法 =====

  private _findElementById(id: string): EditorElement | undefined {
    return this.store.editorElements.find((el) => el.id === id);
  }

  private _ensureCanvas(): boolean {
    return !!this.canvas;
  }

  private _createPlacementFromFabricObject(
    fabricObject: fabric.Object
  ): Partial<Placement> {
    return {
      x: fabricObject.left,
      y: fabricObject.top,
      width: fabricObject.width,
      height: fabricObject.height,
      rotation: fabricObject.angle,
      scaleX: fabricObject.scaleX,
      scaleY: fabricObject.scaleY,
      flipX: fabricObject.flipX,
      flipY: fabricObject.flipY,
    };
  }

  private _hasSignificantScaling(scaleX: number, scaleY: number): boolean {
    return (
      Math.abs(scaleX - 1) > SCALING_THRESHOLD ||
      Math.abs(scaleY - 1) > SCALING_THRESHOLD
    );
  }

  private _handleScaling(
    target: fabric.Object,
    currentPlacement: Placement,
    isTextElement: boolean = false,
    currentFontSize?: number
  ): { placement: Partial<Placement>; fontSize?: number } {
    const scaleX = target.scaleX || 1;
    const scaleY = target.scaleY || 1;
    const newPlacement: Partial<Placement> = { ...currentPlacement };
    let fontSize = currentFontSize;

    // 对于文本元素，保持原始宽度和高度，不应用缩放
    if (isTextElement) {
      // 保持原始尺寸，不乘以缩放因子
      if (target.width !== undefined) newPlacement.width = target.width;
      if (target.height !== undefined) newPlacement.height = target.height;

      // 始终保留缩放值，不重置
      newPlacement.scaleX = scaleX;
      newPlacement.scaleY = scaleY;

      // 不修改字体大小，保持原始值
      // fontSize 保持不变
    } else {
      // 对于非文本元素，保持原有逻辑
      if (target.width !== undefined)
        newPlacement.width = target.width * scaleX;
      if (target.height !== undefined)
        newPlacement.height = target.height * scaleY;

      const hasSignificantScaling = this._hasSignificantScaling(scaleX, scaleY);

      if (hasSignificantScaling && !isTextElement) {
        // 对于非文本元素，重置缩放因子
        newPlacement.scaleX = 1;
        newPlacement.scaleY = 1;
      } else {
        newPlacement.scaleX = scaleX;
        newPlacement.scaleY = scaleY;
      }
    }

    return { placement: newPlacement, fontSize };
  }

  /**
   * 计算正多边形的顶点坐标
   * @param sides 边数
   * @param radius 半径
   * @returns 顶点坐标数组
   */
  // ===== 几何计算方法 =====

  calculatePolygonPoints(sides: number, radius: number) {
    const points = [];
    const angleStep = (2 * Math.PI) / sides;

    for (let i = 0; i < sides; i++) {
      const angle = i * angleStep - Math.PI / 2; // 从顶部开始
      points.push({
        x: radius + radius * Math.cos(angle),
        y: radius + radius * Math.sin(angle),
      });
    }

    return points;
  }

  // ===== 元素操作方法 =====

  alignElement(id: string, alignType: string) {
    const element = this._findElementById(id);
    if (!element?.fabricObject || !this._ensureCanvas()) return;

    const action =
      ALIGNMENT_ACTIONS[alignType as keyof typeof ALIGNMENT_ACTIONS];
    if (!action) return;

    action(element.fabricObject, this.canvas);
    element.placement = {
      ...element.placement,
      ...this._createPlacementFromFabricObject(element.fabricObject),
    };
    this.canvas.renderAll();
  }

  toggleLockElement(id: string) {
    const element = this._findElementById(id);
    if (!element?.fabricObject) return;

    element.locked = !element.locked;
    const locked = element.locked;

    element.fabricObject.set({
      selectable: !locked,
      lockMovementX: locked,
      lockMovementY: locked,
      lockRotation: locked,
      lockScalingX: locked,
      lockScalingY: locked,
    });

    this.store.setSelectedElement(null);
    this.canvas.renderAll();
  }

  cloneElement(id: string) {
    const element = this._findElementById(id);
    if (!element || !this._ensureCanvas()) return;

    // 同步当前元素的fabric对象状态到element
    this.syncFabricObjectToElement(element);

    // 创建克隆元素
    const clonedElement = JSON.parse(JSON.stringify(element));
    clonedElement.id = getUid();
    clonedElement.name = `${element.name} (copy)`;

    // 调整位置避免重叠
    if (clonedElement.placement) {
      clonedElement.placement.x += CLONE_OFFSET;
      clonedElement.placement.y += CLONE_OFFSET;
    }

    // 清除fabric对象引用，让它重新创建
    clonedElement.fabricObject = undefined;

    // 处理媒体元素的特殊情况
    if (element.type === "video" || element.type === "audio") {
      const originalElementId = element.properties.elementId;
      const newElementId = originalElementId.replace(
        element.id,
        clonedElement.id
      );
      clonedElement.properties.elementId = newElementId;

      // 克隆媒体DOM元素
      const originalMedia = document.getElementById(originalElementId);
      if (originalMedia) {
        const clonedMedia = originalMedia.cloneNode(true) as HTMLElement;
        clonedMedia.id = newElementId;
        document.body.appendChild(clonedMedia);

        // 保持媒体元素的播放速度和音量
        if (originalMedia instanceof HTMLMediaElement) {
          const clonedMediaElement = clonedMedia as HTMLMediaElement;
          clonedMediaElement.playbackRate = originalMedia.playbackRate;
          clonedMediaElement.volume = originalMedia.volume;
          clonedMediaElement.currentTime = originalMedia.currentTime;

          // 复制自定义属性
          const originalPlaybackSpeed = (element as any).playbackSpeed;
          const originalVolume = (element as any).volume;
          const originalMediaStartTime = (element.properties as any)
            .mediaStartTime;

          if (originalPlaybackSpeed !== undefined) {
            (clonedElement as any).playbackSpeed = originalPlaybackSpeed;
          }
          if (originalVolume !== undefined) {
            (clonedElement as any).volume = originalVolume;
          }
          if (originalMediaStartTime !== undefined) {
            (clonedElement.properties as any).mediaStartTime =
              originalMediaStartTime;
          }
        }
      }
    }

    // 处理图片元素的特殊情况
    if (element.type === "image") {
      const originalElementId = element.properties.elementId;
      const newElementId = originalElementId.replace(
        element.id,
        clonedElement.id
      );
      clonedElement.properties.elementId = newElementId;

      // 克隆图片DOM元素
      const originalImage = document.getElementById(originalElementId);
      if (originalImage) {
        const clonedImage = originalImage.cloneNode(true) as HTMLElement;
        clonedImage.id = newElementId;
        document.body.appendChild(clonedImage);
      }
    }

    // 添加克隆元素到store
    this.store.addEditorElement(clonedElement);

    // 选中新克隆的元素
    this.store.setSelectedElement(clonedElement);

    console.log(`Element ${element.name} cloned as ${clonedElement.name}`);
  }

  private _splitMediaElement(
    originalElement: VideoEditorElement | AudioEditorElement,
    newElement: VideoEditorElement | AudioEditorElement,
    splitTime: number
  ) {
    const originalMedia = document.getElementById(
      originalElement.properties.elementId
    ) as HTMLMediaElement;
    if (!originalMedia) return;

    // Create and append the new media element
    const newMedia = originalMedia.cloneNode(true) as HTMLMediaElement;
    newMedia.id = newElement.properties.elementId;
    document.body.appendChild(newMedia);

    // Calculate and set the media offset time for the new element
    const originalMediaStartTime =
      (originalElement.properties as any).mediaStartTime || 0;
    const playbackSpeed = (originalElement as any).playbackSpeed || 1;
    const additionalOffset =
      ((splitTime - originalElement.timeFrame.start) / 1000) * playbackSpeed;
    const newMediaOffset = originalMediaStartTime + additionalOffset;

    (newElement.properties as any).mediaStartTime = newMediaOffset;
    newMedia.currentTime = newMediaOffset;
    console.log(
      `分割元素 ${originalElement.id}，新偏移: ${newMediaOffset}s, 播放速度: ${playbackSpeed}x`
    );

    // Preserve playback speed
    if (playbackSpeed !== 1) {
      (newElement as any).playbackSpeed = playbackSpeed;
      newMedia.playbackRate = playbackSpeed;
    }

    // Preserve volume
    const volume = (originalElement as any).volume;
    if (volume !== undefined) {
      (newElement as any).volume = volume;
      newMedia.volume = volume;
    } else {
      (newElement as any).volume = 1;
      newMedia.volume = 1;
    }
  }

  /**
   * 在指定时间点分割时间线元素
   * @param id 要分割的元素ID
   * @param splitTime 分割时间点（毫秒）
   */
  splitElement(id: string, splitTime: number) {
    // 查找要分割的元素
    console.log("split element");
    const elementIndex = this.store.editorElements.findIndex(
      (el) => el.id === id
    );
    if (elementIndex === -1) return;

    const element = this.store.editorElements[elementIndex];

    // 检查分割时间是否在元素的时间范围内
    if (
      splitTime <= element.timeFrame.start ||
      splitTime >= element.timeFrame.end
    ) {
      console.warn("Split time must be between element start and end time");
      return;
    }

    // 创建第一个元素（原始元素的前半部分）
    const firstElement = { ...element };
    firstElement.timeFrame = {
      start: element.timeFrame.start,
      end: splitTime,
    };

    // 创建第二个元素（原始元素的后半部分）
    const secondElement = { ...JSON.parse(JSON.stringify(element)) };
    secondElement.id = getUid(); // 生成新的ID
    secondElement.name = `${element.name} (split)`;
    secondElement.timeFrame = {
      start: splitTime,
      end: element.timeFrame.end,
    };

    // 处理特殊类型元素的额外属性
    if (element.type === "video" || element.type === "audio") {
      // 为第二个元素创建新的媒体元素ID
      const originalElementId = element.properties.elementId;
      const newElementId = originalElementId.replace(
        element.id,
        secondElement.id
      );
      secondElement.properties.elementId = newElementId;
      this._splitMediaElement(element, secondElement, splitTime);
    }

    // 清除第二个元素的fabricObject引用，让它重新创建
    secondElement.fabricObject = undefined;

    // 更新第一个元素（原始元素）
    this.store.updateEditorElement(firstElement);

    // 添加第二个元素
    this.store.addEditorElement(secondElement);

    // 刷新元素和保存更改
    this.store.refreshElements();

    // 更新最大时间
    this.store.updateMaxTime();

    this.store.saveChange();
  }

  deleteElement(id: string) {
    console.log(`ElementManager.deleteElement called for id: ${id}`);

    const elementIndex = this.store.editorElements.findIndex(
      (el) => el.id === id
    );
    if (elementIndex === -1) return;
    const element = this.store.editorElements[elementIndex];

    const currentTime = this.store.currentTimeInMs;

    console.log(`Removing animations for element: ${id}`);
    this.store.animations = this.store.animations.filter(
      (animation) => animation.targetId !== id
    );

    if (element.type === "text" && element.properties.splittedTexts) {
      element.properties.splittedTexts.forEach((textObject) => {
        this.store.canvas?.remove(textObject);
      });
      element.properties.splittedTexts = [];
    }

    if (element.fabricObject) {
      this.canvas.remove(element.fabricObject);
    }

    if (
      element.type === "image" ||
      element.type === "video" ||
      element.type === "audio"
    ) {
      const htmlElement = document.getElementById(
        element.properties?.elementId
      );
      if (htmlElement) {
        htmlElement.remove();
      }
    }

    this.store.setEditorElements(
      this.store.editorElements.filter((el) => el.id !== id)
    );

    this.store.setSelectedElement(null);
    this.store.animationManager?.refreshAnimations();
    this.canvas.renderAll();
    this.store.updateMaxTime();

    if (this.store.maxDuration > 0 && currentTime > this.store.maxDuration) {
      this.store.handleSeek(this.store.maxDuration);
    }

    return element;
  }

  setElementFullscreen(id: string) {
    const element = this._findElementById(id);
    if (!element || !element.fabricObject) return;

    const canvasWidth = this.canvas.getWidth();
    const canvasHeight = this.canvas.getHeight();

    element.fabricObject.set({
      width: canvasWidth,
      height: canvasHeight,
      scaleX: 1,
      scaleY: 1,
      left: 0,
      top: 0,
      angle: 0,
      flipX: element.fabricObject.flipX,
      flipY: element.fabricObject.flipY,
    });

    element.placement = {
      x: 0,
      y: 0,
      width: canvasWidth,
      height: canvasHeight,
      rotation: 0,
      scaleX: 1,
      scaleY: 1,
      flipX: element.fabricObject.flipX,
      flipY: element.fabricObject.flipY,
    };

    this.store.setSelectedElement(element);
    this.canvas.renderAll();
  }

  updateElementOpacity(id: string, opacity: number) {
    const element = this._findElementById(id);
    if (element && element.fabricObject) {
      element.opacity = opacity;
      element.fabricObject.set("opacity", opacity);
      this.canvas.renderAll();
    }
  }

  moveElement(
    element: EditorElement,
    direction: "up" | "down" | "top" | "bottom"
  ) {
    const index = this.store.editorElements.indexOf(element);
    if (index === -1) {
      console.warn("Element not found in editorElements array");
      return;
    }

    const elements = this.store.editorElements;

    switch (direction) {
      case "down":
        if (index > 0) {
          [elements[index - 1], elements[index]] = [
            elements[index],
            elements[index - 1],
          ];
        }
        break;
      case "up":
        if (index < elements.length - 1) {
          [elements[index], elements[index + 1]] = [
            elements[index + 1],
            elements[index],
          ];
        }
        break;
      case "top":
        if (index > 0) {
          const [movedElement] = elements.splice(index, 1);
          elements.unshift(movedElement);
        }
        break;
      case "bottom":
        if (index < elements.length - 1) {
          const [movedElement] = elements.splice(index, 1);
          elements.push(movedElement);
        }
        break;
    }

    this.store.setEditorElements([...elements]);
    this.updateCanvasOrder();
  }

  updateCanvasOrder() {
    this.store.editorElements.forEach((element, index) => {
      if (element.fabricObject) {
        this.canvas.moveTo(element.fabricObject, index);
      }
    });
    this.canvas.renderAll();
  }

  /**
   * 同步所有fabric对象的当前状态到对应的element数据中
   * 这确保了在refreshElements之前，所有通过canvas控制框修改的状态都被保存
   */
  syncAllFabricObjectsToElements() {
    this.store.editorElements.forEach((element) => {
      if (element.fabricObject) {
        this.syncFabricObjectToElement(element);
      }
    });
  }

  /**
   * 同步单个fabric对象的状态到element数据中
   */
  syncFabricObjectToElement(element: EditorElement) {
    if (!element.fabricObject) return;

    const fabricObject = element.fabricObject;

    // 使用syncFabricPropertiesToElement方法进行完整的属性同步
    const updatedElement = this.syncFabricPropertiesToElement(
      element,
      fabricObject
    );

    // 直接更新store中的element数据，不触发refreshElements
    const elementIndex = this.store.editorElements.findIndex(
      (el) => el.id === element.id
    );
    if (elementIndex !== -1) {
      this.store.editorElements[elementIndex] = updatedElement;
    }
  }

  /**
   * 从Fabric对象中提取颜色值
   * @param fill Fabric对象的fill属性
   * @returns 颜色字符串或undefined
   */
  private extractColorFromFabricObject(fill: any): string | undefined {
    if (typeof fill === "string") {
      return fill;
    }
    // 如果是渐变或图案，返回undefined，保持原有值
    return undefined;
  }

  /**
   * 从Fabric文字对象中提取样式数组
   * @param textObject Fabric文字对象
   * @param currentStyles 当前的样式数组
   * @returns 更新后的样式数组
   */
  private extractStylesFromFabricObject(
    textObject: fabric.TextboxWithPadding,
    currentStyles: string[] = []
  ): string[] {
    const styles: string[] = [];

    if (textObject.fontWeight === "bold") {
      styles.push("bold");
    }
    if (textObject.fontStyle === "italic") {
      styles.push("italic");
    }
    if (textObject.underline) {
      styles.push("underlined");
    }
    if (textObject.linethrough) {
      styles.push("strikethrough");
    }

    return styles;
  }

  updateTextStyle(
    elementId: string,
    style: Partial<TextEditorElement["properties"]>
  ) {
    const element = this._findElementById(elementId);
    if (
      !element ||
      element.type !== "text" ||
      !(element.fabricObject instanceof fabric.TextboxWithPadding)
    )
      return;

    // 更新元素properties
    element.properties = { ...element.properties, ...style };
    const textObject = element.fabricObject;

    // 应用基础样式
    this._applyBasicTextStyles(textObject, style);

    // 应用文本样式
    if (style.styles) {
      this._applyTextFormatStyles(textObject, style.styles);
    }

    // 应用阴影
    if (this._hasShadowProperties(style)) {
      this._applyShadowStyle(textObject, style, element.properties);
    }

    // 应用渐变
    if (style.useGradient !== undefined) {
      this._applyGradientStyle(textObject, style);
    }

    // 应用背景色
    if (style.backgroundColor) {
      this._applyBackgroundStyle(textObject, style.backgroundColor);
    }

    textObject.setCoords();
    this.canvas.requestRenderAll();
    this.store.updateSelectedElement();
  }

  private _applyBasicTextStyles(
    textObject: fabric.TextboxWithPadding,
    style: Partial<TextEditorElement["properties"]>
  ) {
    Object.entries(BASIC_STYLE_UPDATES).forEach(([styleKey, fabricKey]) => {
      const value = style[styleKey as keyof typeof style];
      if (value !== undefined) {
        textObject.set(fabricKey as any, value);
      }
    });
  }

  private _applyTextFormatStyles(
    textObject: fabric.TextboxWithPadding,
    styles: string[]
  ) {
    textObject.set({
      fontWeight: styles.includes("bold") ? "bold" : "normal",
      fontStyle: (styles.includes("italic") ? "italic" : "normal") as
        | ""
        | "normal"
        | "italic"
        | "oblique",
      underline: styles.includes("underlined"),
      linethrough: styles.includes("strikethrough"),
    });
  }

  private _hasShadowProperties(
    style: Partial<TextEditorElement["properties"]>
  ): boolean {
    return !!(
      style.shadowColor ||
      style.shadowBlur !== undefined ||
      style.shadowOffsetX !== undefined ||
      style.shadowOffsetY !== undefined
    );
  }

  private _applyShadowStyle(
    textObject: fabric.TextboxWithPadding,
    style: Partial<TextEditorElement["properties"]>,
    currentProps: TextEditorElement["properties"]
  ) {
    textObject.set(
      "shadow",
      new fabric.Shadow({
        color: style.shadowColor || currentProps.shadowColor || "#000000",
        blur: style.shadowBlur ?? currentProps.shadowBlur ?? 0,
        offsetX: style.shadowOffsetX ?? currentProps.shadowOffsetX ?? 0,
        offsetY: style.shadowOffsetY ?? currentProps.shadowOffsetY ?? 0,
      })
    );
  }

  private _applyGradientStyle(
    textObject: fabric.TextboxWithPadding,
    style: Partial<TextEditorElement["properties"]>
  ) {
    if (style.useGradient && style.gradientColors) {
      const gradient = new fabric.Gradient({
        type: "linear",
        coords: { x1: 0, y1: 0, x2: textObject.width!, y2: 0 },
        colorStops: [
          { offset: 0, color: style.gradientColors[0] },
          { offset: 1, color: style.gradientColors[1] },
        ],
      });
      textObject.set("fill", gradient);
    } else if (style.fontColor) {
      textObject.set("fill", style.fontColor);
    }
  }

  private _applyBackgroundStyle(
    textObject: fabric.TextboxWithPadding,
    backgroundColor: string
  ) {
    textObject.set({
      backgroundColor,
      //@ts-ignore
      rx: DEFAULT_BORDER_RADIUS,
      //@ts-ignore
      ry: DEFAULT_BORDER_RADIUS,
    });
  }

  // ===== 元素重排方法 =====

  // ===== 裁剪功能方法 =====

  startCropMode(id: string) {
    const element = this._findElementById(id);
    if (!element || !element.fabricObject || !this._ensureCanvas()) return;

    // 先清理之前的事件监听器（如果有）
    if (this.cropEventHandler) {
      this.canvas.off("object:scaling", this.cropEventHandler);
    }

    this.store.cropObject = element.fabricObject;
    const obj = this.store.cropObject;

    // Create crop rectangle
    this.store.cropRect = new fabric.Rect({
      left: obj.left,
      top: obj.top,
      width: obj.width! * obj.scaleX!,
      height: obj.height! * obj.scaleY!,
      fill: "rgba(0,0,0,0.3)",
      stroke: "#fff",
      strokeWidth: 2,
      strokeDashArray: [5, 5],
      selectable: true,
      hasControls: true,
    });

    this.canvas.add(this.store.cropRect);
    this.canvas.setActiveObject(this.store.cropRect);

    // Add event listener for object scaling
    this.cropEventHandler = (e) => {
      if (e.target === this.store.cropRect) {
        const rect = e.target as fabric.Rect;
        rect.set({
          width: rect.width! * rect.scaleX!,
          height: rect.height! * rect.scaleY!,
          scaleX: 1,
          scaleY: 1,
        });
      }
    };

    this.canvas.on("object:scaling", this.cropEventHandler);
    this.canvas.renderAll();
  }

  applyCrop() {
    if (!this.store.cropObject || !this.store.cropRect || !this._ensureCanvas())
      return;

    const obj = this.store.cropObject as fabric.CoverImage;
    const rect = this.store.cropRect;

    // 获取原始图片尺寸
    const originalSize = (obj as any).getOriginalSize();

    // 计算当前图片对象在画布上的实际显示尺寸
    const currentDisplayWidth = obj.width! * obj.scaleX!;
    const currentDisplayHeight = obj.height! * obj.scaleY!;

    // 计算剪裁矩形相对于图片对象的位置和尺寸
    const relativeLeft = rect.left! - obj.left!;
    const relativeTop = rect.top! - obj.top!;
    const relativeWidth = rect.width!;
    const relativeHeight = rect.height!;

    // 将相对位置转换为原始图片坐标系中的位置
    // 这里需要考虑图片的缩放比例
    const scaleX = originalSize.width / currentDisplayWidth;
    const scaleY = originalSize.height / currentDisplayHeight;

    const cropX = relativeLeft * scaleX;
    const cropY = relativeTop * scaleY;
    const cropWidth = relativeWidth * scaleX;
    const cropHeight = relativeHeight * scaleY;

    console.log("Crop calculation:", {
      originalSize,
      currentDisplay: {
        width: currentDisplayWidth,
        height: currentDisplayHeight,
      },
      relative: {
        left: relativeLeft,
        top: relativeTop,
        width: relativeWidth,
        height: relativeHeight,
      },
      scale: { x: scaleX, y: scaleY },
      crop: { x: cropX, y: cropY, width: cropWidth, height: cropHeight },
    });

    // 设置裁剪参数到 fabric 对象
    obj.set({
      cropX,
      cropY,
      cropWidth,
      cropHeight,
      width: rect.width,
      height: rect.height,
      left: rect.left,
      top: rect.top,
      scaleX: 1,
      scaleY: 1,
    });

    // 移除裁剪矩形
    this.canvas.remove(this.store.cropRect);
    this.store.cropRect = null;
    this.store.cropObject = null;

    // 更新编辑器元素
    const element = this.store.editorElements.find(
      (el) => el.fabricObject === obj
    );
    if (element) {
      element.placement = {
        x: rect.left!,
        y: rect.top!,
        width: rect.width!,
        height: rect.height!,
        cropX: cropX,
        cropY: cropY,
        cropHeight: cropHeight,
        cropWidth: cropWidth,
        rotation: obj.angle!,
        scaleX: 1,
        scaleY: 1,
        flipX: obj.flipX,
        flipY: obj.flipY,
      };
      this.store.updateEditorElement(element, "元素属性修改");
    }

    // 重新渲染画布
    this.canvas.requestRenderAll();
  }

  cancelCrop() {
    if (!this.store.cropRect || !this.canvas) return;

    // 清理事件监听器
    if (this.cropEventHandler) {
      this.canvas.off("object:scaling", this.cropEventHandler);
      this.cropEventHandler = null;
    }

    this.canvas.remove(this.store.cropRect);
    this.store.cropRect = null;
    this.store.cropObject = null;
    this.store.canvas.renderAll();
  }

  // ===== 元素同步和刷新方法 =====

  refreshElements() {
    const store = this.store;
    if (!store.canvas) return;

    // 先根据轨道顺序更新元素数组
    store.updateCanvasOrderByTrackOrder();

    // 清除画布上的所有对象
    store.canvas.remove(...store.canvas.getObjects());

    // 清理事件监听器并确保所有元素的fabricObject为null，以便重新创建
    store.editorElements.forEach((element) => {
      if (element.fabricObject) {
        // 移除所有事件监听器以防止内存泄漏
        element.fabricObject.off();
        element.fabricObject = null;
      }
    });

    // 按照editorElements数组的顺序添加元素到画布
    // 注意：我们从后往前添加，这样第一个元素会显示在最上面
    for (let index = store.editorElements.length - 1; index >= 0; index--) {
      const element = store.editorElements[index];
      this.addElement(element);
    }

    store.refreshAnimations();
    store.updateTimeTo(store.currentTimeInMs);
    store.canvas.requestRenderAll();
  }

  // ===== 媒体效果设置方法 =====

  setMediaFilter(
    id: string,
    filterType: "brightness" | "contrast" | "saturation" | "hue" | "blur",
    value: number
  ) {
    const element = this._findElementById(id);
    if (
      !element?.fabricObject ||
      !(
        element.fabricObject instanceof fabric.CoverImage ||
        element.fabricObject instanceof fabric.CoverVideo
      )
    )
      return;

    const mediaObject = element.fabricObject;
    const methodName = FILTER_METHOD_MAP[filterType];

    if (methodName && typeof (mediaObject as any)[methodName] === "function") {
      (mediaObject as any)[methodName](value);
    }

    if (isEditorMediaElement(element)) {
      element.properties = {
        ...element.properties,
        filters: {
          brightness: 0,
          contrast: 0,
          saturation: 0,
          vibrance: 0,
          hue: 0,
          noise: 0,
          blur: 0,
          ...element.properties.filters,
          [filterType]: value,
        },
      };
    }

    this.canvas.requestRenderAll();
  }

  setMediaBorder(id: string, border: Partial<BorderStyle>) {
    const element = this._findElementById(id);
    if (
      !element ||
      !element.fabricObject ||
      !(element.fabricObject instanceof fabric.CoverImage)
    )
      return;

    if (element && isEditorImageElement(element)) {
      element.properties = {
        ...element.properties,
        border: {
          ...element.properties.border,
          ...border,
        },
      };
    }

    this.canvas.renderAll();
  }

  destroy() {
    // 清理事件监听器
    if (this.cropEventHandler && this.canvas) {
      this.canvas.off("object:scaling", this.cropEventHandler);
      this.cropEventHandler = null;
    }

    // Clear any references to fabric objects in the store
    this.store.editorElements.forEach((element) => {
      if (element.fabricObject) {
        // 移除所有事件监听器
        element.fabricObject.off();
        element.fabricObject = null;
      }
    });

    // Clear the elements array in the store
    this.store.setEditorElements([]);

    // Clear all tracks when destroying elements
    this.store.trackManager.tracks = [];
    this.store.trackManager.defaultTracks = {
      media: "",
      audio: "",
      text: "",
      caption: "",
    };

    // Clear crop-related objects
    this.store.cropObject = null;
    this.store.cropRect = null;
  }

  // ===== Fabric属性获取方法 =====

  private _getBasePlacementProperties(placement: Placement) {
    return {
      left: placement.x,
      top: placement.y,
      width: placement.width,
      height: placement.height,
      scaleX: placement.scaleX,
      scaleY: placement.scaleY,
      angle: placement.rotation,
      flipX: placement.flipX || false,
      flipY: placement.flipY || false,
    };
  }

  private _getCommonElementProperties(element: EditorElement) {
    return {
      ...this._getBasePlacementProperties(element.placement),
      opacity: element.opacity,
      // @ts-ignore
      locked: element.locked,
      selectable: !element.locked,
    };
  }

  private _getCommonMediaFabricProperties(
    element: VideoEditorElement | ImageEditorElement
  ) {
    return {
      ...this._getCommonElementProperties(element),
      cropX: element.placement.cropX,
      cropY: element.placement.cropY,
      cropHeight: element.placement.cropHeight,
      cropWidth: element.placement.cropWidth,
      brightness: element.properties.filters.brightness,
      contrast: element.properties.filters.contrast,
      saturation: element.properties.filters.saturation,
      hue: element.properties.filters.hue,
      blur: element.properties.filters.blur,
      imageBorderColor: element.properties.border.color,
      borderWidth: element.properties.border.width,
      borderStyle: element.properties.border.style,
      borderRadius: element.properties.border.borderRadius,
      // @ts-ignore
      customFilter: element.properties.effect.type,
    };
  }
  private _getTextFabricProperties(element: TextEditorElement) {
    const { properties: props } = element;
    const styles = Array.isArray(props.styles) ? props.styles : [];

    return {
      ...this._getCommonElementProperties(element),
      text: props.text,
      fontSize: props.fontSize,
      fontFamily: props.fontFamily || "Arial",
      textAlign: props.textAlign || "left",
      lineHeight: props.lineHeight || 1,
      charSpacing: props.charSpacing || 0,
      fontWeight: (styles.includes("bold") || props.fontWeight === 700
        ? "bold"
        : "normal") as "normal" | "bold",
      fontStyle: (styles.includes("italic") ? "italic" : "normal") as
        | ""
        | "normal"
        | "italic"
        | "oblique",
      underline: styles.includes("underlined"),
      linethrough: styles.includes("strikethrough"),
      strokeWidth: props.strokeWidth || 0,
      stroke: props.strokeColor || "#000000",
      backgroundColor: props.backgroundColor,
    };
  }

  refreshElement(element: EditorElement) {
    if (!this.canvas || !element.fabricObject) return;

    switch (element.type) {
      case "video":
      case "image": {
        element.fabricObject.set(
          this._getCommonMediaFabricProperties(element) as any
        );
        break;
      }
      case "text": {
        const textObject = element.fabricObject as fabric.TextboxWithPadding;
        textObject.set(this._getTextFabricProperties(element));
        this._applyTextStyles(textObject, element.properties);
        break;
      }
      case "shape": {
        const shapeElement = element as ShapeEditorElement;
        const shapeObject = element.fabricObject;

        shapeObject.set(this._getShapeFabricProperties(shapeElement));

        if (
          shapeElement.properties.shapeType === "roundedRect" &&
          shapeObject instanceof fabric.Rect
        ) {
          shapeObject.set({
            rx: shapeElement.properties.borderRadius || DEFAULT_BORDER_RADIUS,
            ry: shapeElement.properties.borderRadius || DEFAULT_BORDER_RADIUS,
          });
        }

        break;
      }
    }

    element.fabricObject.setCoords();
    this.canvas.requestRenderAll();
  }

  private _applyTextStyles(
    textObject: fabric.TextboxWithPadding,
    properties: TextEditorElement["properties"]
  ) {
    // Gradient
    if (properties.useGradient && properties.gradientColors) {
      const gradient = new fabric.Gradient({
        type: "linear",
        coords: { x1: 0, y1: 0, x2: textObject.width, y2: 0 },
        colorStops: [
          { offset: 0, color: properties.gradientColors[0] },
          { offset: 1, color: properties.gradientColors[1] },
        ],
      });
      textObject.set("fill", gradient);
    } else {
      textObject.set("fill", properties.fontColor || "#ffffff");
    }

    // Shadow
    textObject.set(
      "shadow",
      new fabric.Shadow({
        color: properties.shadowColor || "#000000",
        blur: properties.shadowBlur || 0,
        offsetX: properties.shadowOffsetX || 0,
        offsetY: properties.shadowOffsetY || 0,
      })
    );
  }

  private _getShapeFabricProperties(element: ShapeEditorElement) {
    return {
      ...this._getCommonElementProperties(element),
      fill: element.properties.fill,
      stroke: element.properties.stroke,
      strokeWidth: element.properties.strokeWidth,
      backgroundColor: element.properties.backgroundColor || "transparent",
    };
  }

  /**
   * 同步fabric对象属性到element（改进版本）
   * 确保所有通过canvas控制修改的属性都被正确保存
   */
  private syncFabricPropertiesToElement(
    element: EditorElement,
    target: fabric.Object
  ): EditorElement {
    // 基础placement属性同步
    const newPlacement: Placement = {
      ...element.placement,
      x: target.left ?? element.placement.x,
      y: target.top ?? element.placement.y,
      rotation: target.angle ?? element.placement.rotation,
      flipX: target.flipX ?? element.placement.flipX,
      flipY: target.flipY ?? element.placement.flipY,
    };

    // 基础元素更新（保留所有原始属性）
    let updatedElement: EditorElement = {
      ...element,
      placement: newPlacement,
      // 同步通用属性
      opacity: target.opacity ?? element.opacity,
      locked: (target as any).locked ?? element.locked,
    };

    // 根据元素类型处理特定属性
    if (element.type === "text") {
      const textElement = element as TextEditorElement;
      const textTarget = target as fabric.TextboxWithPadding;

      // 获取当前fabric对象的字体大小
      const currentFabricFontSize =
        textTarget.fontSize || textElement.properties.fontSize || 100;

      // 使用统一的缩放处理逻辑，文本元素自动保留缩放值
      const scalingResult = this._handleScaling(
        target,
        newPlacement,
        true,
        currentFabricFontSize
      );

      const newProperties = {
        ...textElement.properties,
        text: textTarget.text || textElement.properties.text,
        fontSize: scalingResult.fontSize || currentFabricFontSize,
        fontFamily: textTarget.fontFamily || textElement.properties.fontFamily,
        fontColor:
          this.extractColorFromFabricObject(textTarget.fill) ||
          textElement.properties.fontColor,
        textAlign:
          (textTarget.textAlign as "left" | "center" | "right") ||
          textElement.properties.textAlign,
        lineHeight: textTarget.lineHeight || textElement.properties.lineHeight,
        charSpacing:
          textTarget.charSpacing || textElement.properties.charSpacing,
        strokeWidth:
          textTarget.strokeWidth || textElement.properties.strokeWidth,
        strokeColor: textTarget.stroke || textElement.properties.strokeColor,
        backgroundColor:
          textTarget.backgroundColor || textElement.properties.backgroundColor,
        // 同步样式数组
        styles: this.extractStylesFromFabricObject(
          textTarget,
          Array.isArray(textElement.properties.styles)
            ? textElement.properties.styles
            : []
        ),
      };

      updatedElement = {
        ...textElement,
        placement: { ...newPlacement, ...scalingResult.placement },
        properties: newProperties,
        // 同步通用属性
        opacity: target.opacity ?? textElement.opacity,
        locked: (target as any).locked ?? textElement.locked,
      };
    } else {
      // 对于非文本元素（图片、视频、形状等）
      const scalingResult = this._handleScaling(target, newPlacement, false);

      // 同步裁剪属性（如果存在）
      const targetWithCrop = target as any;
      const cropProperties: Partial<Placement> = {};
      if (targetWithCrop.cropX !== undefined)
        cropProperties.cropX = targetWithCrop.cropX;
      if (targetWithCrop.cropY !== undefined)
        cropProperties.cropY = targetWithCrop.cropY;
      if (targetWithCrop.cropWidth !== undefined)
        cropProperties.cropWidth = targetWithCrop.cropWidth;
      if (targetWithCrop.cropHeight !== undefined)
        cropProperties.cropHeight = targetWithCrop.cropHeight;

      updatedElement = {
        ...updatedElement,
        placement: {
          ...newPlacement,
          ...scalingResult.placement,
          ...cropProperties,
        },
      };
    }

    return updatedElement;
  }

  private _ensureMediaElementExists(
    element: EditorElement
  ): HTMLElement | null {
    if (
      (element.type !== "video" &&
        element.type !== "audio" &&
        element.type !== "image") ||
      !element.properties?.elementId
    ) {
      return null;
    }

    const mediaElementId = element.properties.elementId;
    let existingElement = document.getElementById(mediaElementId);

    if (existingElement) {
      return existingElement;
    }

    console.log(
      `Creating missing media element: ${mediaElementId}, type: ${element.type}`
    );

    let newMediaElement: HTMLElement | null = null;
    if (element.type === "video") {
      const videoElement = document.createElement("video");
      videoElement.id = mediaElementId;
      videoElement.src = element.properties.src;
      videoElement.crossOrigin = "anonymous";
      videoElement.style.display = "none";
      if ((element.properties as any).mediaStartTime) {
        videoElement.currentTime = (element.properties as any).mediaStartTime;
      }
      newMediaElement = videoElement;
    } else if (element.type === "audio") {
      const audioElement = document.createElement("audio");
      audioElement.id = mediaElementId;
      audioElement.src = element.properties.src;
      audioElement.crossOrigin = "anonymous";
      audioElement.style.display = "none";
      if ((element.properties as any).mediaStartTime) {
        audioElement.currentTime = (element.properties as any).mediaStartTime;
      }
      newMediaElement = audioElement;
    } else if (element.type === "image") {
      const imageElement = document.createElement("img");
      imageElement.id = mediaElementId;
      imageElement.src = element.properties.src;
      imageElement.crossOrigin = "anonymous";
      imageElement.style.display = "none";
      newMediaElement = imageElement;
    }

    if (newMediaElement) {
      document.body.appendChild(newMediaElement);
    }
    return newMediaElement;
  }

  private _getCommonMediaFabricCreationProperties(
    element: VideoEditorElement | ImageEditorElement
  ) {
    return {
      name: element.id,
      left: element.placement.x,
      locked: element.locked,
      opacity: element.opacity,
      top: element.placement.y,
      cropX: element.placement.cropX,
      cropY: element.placement.cropY,
      width: element.placement.width,
      height: element.placement.height,
      cropHeight: element.placement.cropHeight,
      cropWidth: element.placement.cropWidth,
      scaleX: element.placement.scaleX,
      scaleY: element.placement.scaleY,
      angle: element.placement.rotation,
      flipX: element.placement.flipX,
      flipY: element.placement.flipY,
      brightness: element.properties.filters.brightness,
      contrast: element.properties.filters.contrast,
      saturation: element.properties.filters.saturation,
      hue: element.properties.filters.hue,
      blur: element.properties.filters.blur,
      imageBorderColor: element.properties.border.color,
      borderWidth: element.properties.border.width,
      borderStyle: element.properties.border.style,
      borderRadius: element.properties.border.borderRadius,
      selectable: !element.locked,
      lockUniScaling: true,
      // @ts-ignore
      customFilter: element.properties.effect.type,
    };
  }
  private _addVideoElement(
    element: VideoEditorElement,
    modifiedHandler: (e: fabric.IEvent<Event>) => void
  ) {
    const videoElement = document.getElementById(element.properties.elementId);
    if (!isHtmlVideoElement(videoElement)) {
      console.error(
        `Video element not found or invalid: ${element.properties.elementId}`
      );
      return;
    }
    const videoObject = new CoverVideo(videoElement, {
      ...this._getCommonMediaFabricCreationProperties(element),
      objectCaching: false,
    });
    element.fabricObject = videoObject;
    videoElement.width = 200;
    videoElement.height =
      (videoElement.videoHeight * 200) / videoElement.videoWidth;
    this.store.canvas.add(videoObject);
    videoObject.on("modified", modifiedHandler);
  }
  private _addImageElement(
    element: ImageEditorElement,
    modifiedHandler: (e: fabric.IEvent<Event>) => void
  ) {
    const imageElement = document.getElementById(element.properties.elementId);
    if (!isHtmlImageElement(imageElement)) return;
    const imageObject = new CoverImage(imageElement, {
      ...this._getCommonMediaFabricCreationProperties(element),
      objectCaching: true,
    });
    element.fabricObject = imageObject;
    const imageSize = {
      width: imageElement.naturalWidth,
      height: imageElement.naturalHeight,
    };
    imageElement.width = 200;
    if (imageElement.width > 0) {
      imageElement.height = (imageElement.height * 200) / imageElement.width;
    } else {
      imageElement.height = 200;
    }
    this.store.canvas.add(imageObject);
    imageObject.on("modified", modifiedHandler);
  }
  private _addTextElement(
    element: TextEditorElement,
    modifiedHandler: (e: fabric.IEvent<Event>) => void
  ) {
    const textFabricProps = this._getTextFabricProperties(element);
    const textObject = new fabric.TextboxWithPadding(element.properties.text, {
      name: element.id,
      ...textFabricProps,
      padding: 5, // 修改为与后端 PADDING_X 一致
      objectCaching: false,
      selectable: true,
      lockUniScaling: true,
      //@ts-ignore
      rx: DEFAULT_BORDER_RADIUS,
      //@ts-ignore
      ry: DEFAULT_BORDER_RADIUS,
    });

    this._applyTextStyles(textObject, element.properties);

    element.fabricObject = textObject;
    this.store.canvas.add(textObject);
    textObject.on("modified", modifiedHandler);
  }
  private _addShapeElement(
    element: ShapeEditorElement,
    modifiedHandler: (e: fabric.IEvent<Event>) => void
  ) {
    const commonProps = {
      name: element.id,
      ...this._getShapeFabricProperties(element),
      objectCaching: true,
      lockUniScaling: true,
    };

    const shapeObject = this._createShapeObject(
      element.properties.shapeType,
      commonProps,
      element.properties
    );
    element.fabricObject = shapeObject;
    this.store.canvas.add(shapeObject);
    shapeObject.on("modified", modifiedHandler);
  }

  private _createShapeObject(
    shapeType: string,
    commonProps: any,
    properties: ShapeEditorElement["properties"]
  ): fabric.Object {
    const { width, height } = commonProps;

    const shapeCreators = {
      rect: () => new fabric.Rect(commonProps),
      roundedRect: () =>
        new fabric.Rect({
          ...commonProps,
          rx: properties.borderRadius || DEFAULT_BORDER_RADIUS,
          ry: properties.borderRadius || DEFAULT_BORDER_RADIUS,
        }),
      circle: () =>
        new fabric.Circle({
          ...commonProps,
          radius: Math.min(width, height) / 2,
        }),
      ellipse: () =>
        new fabric.Ellipse({
          ...commonProps,
          rx: width / 2,
          ry: height / 2,
        }),
      triangle: () => new fabric.Triangle(commonProps),
      line: () =>
        new fabric.Line([0, 0, width, 0], {
          ...commonProps,
          stroke: properties.stroke,
          strokeWidth: properties.strokeWidth || 5,
        }),
      polygon: () => this._createPolygonShape(5, commonProps),
      pentagon: () => this._createPolygonShape(5, commonProps),
      hexagon: () => this._createPolygonShape(6, commonProps),
      octagon: () => this._createPolygonShape(8, commonProps),
      parallelogram: () => this._createParallelogram(commonProps),
      arch: () => this._createArch(commonProps),
    };

    const creator = shapeCreators[shapeType] || shapeCreators.rect;
    return creator();
  }

  private _createPolygonShape(sides: number, commonProps: any): fabric.Polygon {
    return new fabric.Polygon(
      this.calculatePolygonPoints(sides, commonProps.width / 2),
      commonProps
    );
  }

  private _createParallelogram(commonProps: any): fabric.Polygon {
    const { width, height } = commonProps;
    const offset = width / 4;
    return new fabric.Polygon(
      [
        { x: offset, y: 0 },
        { x: width, y: 0 },
        { x: width - offset, y: height },
        { x: 0, y: height },
      ],
      commonProps
    );
  }

  private _createArch(commonProps: any): fabric.Path {
    const { width, height } = commonProps;
    const archPath = `M 0 ${height} L 0 ${height / 2} Q ${
      width / 2
    } 0 ${width} ${height / 2} L ${width} ${height} Z`;
    return new fabric.Path(archPath, commonProps);
  }

  addElement(newElement: EditorElement) {
    if (!this.store.canvas) return;

    const element = this.store.editorElements.find(
      (el) => el.id === newElement.id
    );
    if (!element) return;

    this._ensureMediaElementExists(element);

    const objectModifiedHandler = this._createObjectModifiedHandler();
    const elementAddMethods = {
      video: () =>
        this._addVideoElement(
          element as VideoEditorElement,
          objectModifiedHandler
        ),
      image: () =>
        this._addImageElement(
          element as ImageEditorElement,
          objectModifiedHandler
        ),
      text: () =>
        this._addTextElement(
          element as TextEditorElement,
          objectModifiedHandler
        ),
      shape: () =>
        this._addShapeElement(
          element as ShapeEditorElement,
          objectModifiedHandler
        ),
      audio: () => {}, // Audio elements don't have fabric objects
    };

    const addMethod = elementAddMethods[element.type];
    if (addMethod) {
      addMethod();
      this._setupElementSelection(element);
    } else {
      throw new Error(`Element type ${element.type} not implemented`);
    }
  }

  private _createObjectModifiedHandler() {
    return (e: fabric.IEvent) => {
      if (!e.target) return;

      const modifiedElement = this.store.editorElements.find(
        (el) => el.fabricObject === e.target
      );
      if (!modifiedElement) return;

      const updatedElement = this.syncFabricPropertiesToElement(
        modifiedElement,
        e.target
      );
      this.store.updateEditorElement(updatedElement);
    };
  }

  private _setupElementSelection(element: EditorElement) {
    if (element.fabricObject) {
      element.fabricObject.on("selected", () => {
        if (this.store.selectedElement?.id !== element.id) {
          this.store.setSelectedElement(element);
        }
      });
    }
  }

  // ===== 元素创建工厂方法 =====

  private _createCenteredPlacement(width: number, height: number) {
    const canvasWidth =
      this.canvas?.getWidth() || ELEMENT_CONSTANTS.CANVAS.DEFAULT_WIDTH;
    const canvasHeight =
      this.canvas?.getHeight() || ELEMENT_CONSTANTS.CANVAS.DEFAULT_HEIGHT;
    return {
      x: Number(((canvasWidth - width) / 2).toFixed(2)),
      y: Number(((canvasHeight - height) / 2).toFixed(2)),
      width: Number(width.toFixed(2)),
      height: Number(height.toFixed(2)),
      rotation: 0,
      scaleX: 1,
      scaleY: 1,
      flipX: false,
      flipY: false,
    };
  }

  private _createBaseElement(
    id: string,
    type: string,
    name: string,
    timeFrame: TimeFrame,
    placement: any,
    properties: any
  ): EditorElement {
    return {
      id,
      locked: false,
      opacity: 1,
      name,
      type: type as any,
      placement,
      timeFrame,
      properties,
    };
  }

  private _createMediaProperties(
    elementId: string,
    src: string,
    originalDuration?: number
  ) {
    const baseProperties = {
      elementId,
      src,
      effect: { type: "none" },
      filters: { type: "none" },
      border: {
        width: 0,
        color: "black",
        style: "solid",
        borderRadius: 0,
      },
    };

    if (originalDuration !== undefined) {
      return { ...baseProperties, originalDuration };
    }

    return baseProperties;
  }

  private _addMediaElement(element: EditorElement) {
    this.store.addEditorElement(element);
    this.store.saveChange();
  }

  addVideoElement(
    videoElement: HTMLVideoElement,
    videoElementId: string,
    videoMetadata?: any
  ) {
    const videoDurationMs = Number((videoElement.duration * 1000).toFixed(2));
    const aspectRatio = videoElement.videoWidth / videoElement.videoHeight;
    const videoHeight = ELEMENT_CONSTANTS.MEDIA.DEFAULT_VIDEO_HEIGHT;
    const videoWidth = videoHeight * aspectRatio;

    // 优先使用元数据中的名称，否则使用默认名称
    let displayName = `Video ${this.store.videos.length + 1}`;
    if (videoMetadata?.name) {
      displayName = videoMetadata.name;
    }

    const element = this._createBaseElement(
      videoElementId,
      MEDIA_TYPES.VIDEO,
      displayName,
      { start: 0, end: videoDurationMs },
      this._createCenteredPlacement(videoWidth, videoHeight),
      this._createMediaProperties(
        `video-${videoElementId}`,
        videoElement.src,
        videoDurationMs
      )
    );

    this._addMediaElement(element);
    this.store.updateMediaElements();
  }

  addVideo(index: string) {
    const videoElement = document.getElementById(`video-${index}`);
    if (!isHtmlVideoElement(videoElement)) {
      return;
    }
    this.addVideoElement(videoElement, index);
  }

  addImageElement(
    imageElement: HTMLImageElement,
    elementId: string,
    imageMetadata?: any
  ) {
    const aspectRatio = imageElement.naturalWidth / imageElement.naturalHeight;
    const imageSize = ELEMENT_CONSTANTS.MEDIA.DEFAULT_IMAGE_SIZE;
    const imageWidth = imageSize * aspectRatio;
    const imageHeight = imageSize;

    // 优先使用元数据中的名称，否则使用默认名称
    let displayName = `Image ${this.store.images.length + 1}`;
    if (imageMetadata?.name) {
      displayName = imageMetadata.name;
    }

    const element = this._createBaseElement(
      elementId,
      MEDIA_TYPES.IMAGE,
      displayName,
      {
        start: 0,
        end: ELEMENT_CONSTANTS.TIMELINE.DEFAULT_ELEMENT_DURATION.IMAGE,
      },
      this._createCenteredPlacement(imageWidth, imageHeight),
      this._createMediaProperties(`image-${elementId}`, imageElement.src)
    );

    this._addMediaElement(element);
  }

  addImage(id: string) {
    const imageElement = document.getElementById(`image-${id}`);
    if (!isHtmlImageElement(imageElement)) {
      return;
    }
    this.addImageElement(imageElement, id);
  }

  addAudioElement(
    audioElement: HTMLAudioElement,
    audioElementId: string,
    audioMetadata?: any
  ) {
    const audioDurationMs = audioElement.duration * 1000;

    // 优先使用元数据中的名称，否则使用默认名称
    let displayName = `Audio ${this.store.audios.length + 1}`;
    if (audioMetadata?.name) {
      displayName = audioMetadata.name;
    }

    const element = this._createBaseElement(
      audioElementId,
      MEDIA_TYPES.AUDIO,
      displayName,
      { start: 0, end: audioDurationMs },
      null, // 音频元素不需要placement
      {
        elementId: `audio-${audioElementId}`,
        src: audioElement.src,
        originalDuration: audioDurationMs,
      }
    );

    this._addMediaElement(element);
  }

  addAudio(index: string) {
    const audioElement = document.getElementById(`audio-${index}`);
    if (!isHtmlAudioElement(audioElement)) {
      return;
    }
    this.addAudioElement(audioElement, index);
  }

  addText(options: {
    text: string;
    fontSize: number;
    fontWeight: number;
    id?: string;
    fontFamily?: string;
    fontColor?: string;
    textAlign?: "left" | "center" | "right";
    lineHeight?: number;
    charSpacing?: number;
    styles?: string[];
    strokeWidth?: number;
    strokeColor?: string;
    shadowBlur?: number;
    shadowOffsetX?: number;
    shadowOffsetY?: number;
    shadowColor?: string;
    useGradient?: boolean;
    gradientColors?: string[];
    backgroundColor?: string;
  }) {
    const {
      text,
      fontSize,
      fontWeight,
      id: optionsId,
      fontFamily = "Arial",
      fontColor = "#ffffff",
      textAlign = "left",
      lineHeight = 1,
      charSpacing = 0,
      styles = [],
      strokeWidth = 0,
      strokeColor = "#000000",
      shadowBlur = 0,
      shadowOffsetX = 0,
      shadowOffsetY = 0,
      shadowColor = "#000000",
      useGradient = false,
      gradientColors = ["#ffffff", "#000000"],
      backgroundColor,
    } = options;
    const elementId = optionsId || getUid();
    const index = this.store.editorElements.length;

    // 创建临时的fabric.Text对象来计算文本尺寸，使用实际的文本属性
    const textConfig = {
      fontSize,
      fontWeight,
      fontFamily,
      textAlign,
      charSpacing,
      lineHeight,
      strokeWidth,
    };
    const tempText = new fabric.Text(text, textConfig);

    // 应用阴影效果到临时文本对象以获得准确尺寸
    if (shadowBlur > 0) {
      tempText.set(
        "shadow",
        new fabric.Shadow({
          color: shadowColor,
          blur: shadowBlur,
          offsetX: shadowOffsetX,
          offsetY: shadowOffsetY,
        })
      );
    }

    // 确保文本完全显示，添加额外的边距以防止截断
    tempText.setCoords();
    const textBounds = tempText.getBoundingRect();
    const textWidth = Math.max(textBounds.width, tempText.width || 0) + 20; // 添加20px边距
    const textHeight = Math.max(textBounds.height, tempText.height || 0) + 20; // 添加20px边距

    const element = this._createBaseElement(
      elementId,
      MEDIA_TYPES.TEXT,
      `Text ${index + 1}`,
      {
        start: 0,
        end: ELEMENT_CONSTANTS.TIMELINE.DEFAULT_ELEMENT_DURATION.TEXT,
      },
      this._createCenteredPlacement(textWidth, textHeight),
      this._createTextProperties(
        text,
        fontSize,
        fontWeight,
        fontFamily,
        fontColor,
        textAlign,
        lineHeight,
        charSpacing,
        styles,
        strokeWidth,
        strokeColor,
        shadowBlur,
        shadowOffsetX,
        shadowOffsetY,
        shadowColor,
        useGradient,
        gradientColors,
        backgroundColor
      )
    );

    this.store.addEditorElement(element);
    this.store.saveChange();
  }

  private _createTextProperties(
    text: string,
    fontSize: number,
    fontWeight: number,
    fontFamily: string = "Arial",
    fontColor: string = "#ffffff",
    textAlign: "left" | "center" | "right" = "left",
    lineHeight: number = 1,
    charSpacing: number = 0,
    styles: string[] = [],
    strokeWidth: number = 0,
    strokeColor: string = "#000000",
    shadowBlur: number = 0,
    shadowOffsetX: number = 0,
    shadowOffsetY: number = 0,
    shadowColor: string = "#000000",
    useGradient: boolean = false,
    gradientColors: string[] = ["#ffffff", "#000000"],
    backgroundColor?: string
  ) {
    return {
      text,
      fontSize,
      fontWeight,
      splittedTexts: [],
      fontFamily,
      textAlign,
      lineHeight,
      charSpacing,
      styles,
      fontColor,
      strokeWidth,
      strokeColor,
      shadowColor,
      shadowBlur,
      shadowOffsetX,
      shadowOffsetY,
      useGradient,
      gradientColors,
      backgroundColor,
    };
  }

  /**
   * 添加图形元素
   * @param shapeType 图形类型
   * @param options 可选配置
   */
  addShapeElement(
    shapeType: ShapeType,
    options?: {
      fill?: string;
      stroke?: string;
      strokeWidth?: number;
      borderRadius?: number;
      id?: string;
    }
  ) {
    const elementId = options?.id || getUid();
    const index = this.store.editorElements.length;
    const { width, height } = this._getShapeDimensions(shapeType);

    const element = this._createBaseElement(
      elementId,
      MEDIA_TYPES.SHAPE,
      `Shape ${index + 1}`,
      {
        start: 0,
        end: ELEMENT_CONSTANTS.TIMELINE.DEFAULT_ELEMENT_DURATION.SHAPE,
      },
      this._createCenteredPlacement(width, height),
      this._createShapeProperties(shapeType, options)
    );

    this.store.addEditorElement(element);
    this.store.saveChange();
  }

  private _getShapeDimensions(shapeType: ShapeType): {
    width: number;
    height: number;
  } {
    const defaultSize = ELEMENT_CONSTANTS.MEDIA.DEFAULT_SHAPE_SIZE;

    switch (shapeType) {
      case "line":
        return {
          width: ELEMENT_CONSTANTS.MEDIA.DEFAULT_LINE_WIDTH,
          height: ELEMENT_CONSTANTS.MEDIA.DEFAULT_LINE_HEIGHT,
        };
      case "ellipse":
      case "parallelogram":
        return {
          width: ELEMENT_CONSTANTS.MEDIA.DEFAULT_ELLIPSE_WIDTH,
          height: ELEMENT_CONSTANTS.MEDIA.DEFAULT_ELLIPSE_HEIGHT,
        };
      default:
        return { width: defaultSize, height: defaultSize };
    }
  }

  private _createShapeProperties(
    shapeType: ShapeType,
    options?: {
      fill?: string;
      stroke?: string;
      strokeWidth?: number;
      borderRadius?: number;
    }
  ) {
    return {
      shapeType,
      fill: options?.fill || "#9e9e9e",
      stroke: options?.stroke || "#757575",
      strokeWidth: options?.strokeWidth || 1,
      borderRadius:
        options?.borderRadius || (shapeType === "roundedRect" ? 10 : 0),
      border: {
        width: 0,
        color: "transparent",
        style: "solid",
        borderRadius: 0,
      },
    };
  }

  // ===== 元素数组管理方法 =====

  /**
   * 设置编辑元素数组
   * @param editorElements 要设置的编辑元素数组
   */
  setEditorElements(editorElements: EditorElement[]) {
    this.store.editorElements = editorElements;
    this.store.updateSelectedElement();

    // Only auto-fit when elements actually exist
    if (editorElements.length > 0) {
      // this.fitTimelineToContent();
    }
  }

  /**
   * 更新编辑元素
   * @param editorElement 要更新的编辑元素
   * @param actionType 操作类型，默认为"元素修改"
   */
  updateEditorElement(
    editorElement: EditorElement,
    actionType: HistoryActionType = "元素修改"
  ) {
    if (!editorElement) return;
    console.log(editorElement);
    this.setEditorElements(
      this.store.editorElements.map((element) =>
        element.id === editorElement.id ? editorElement : element
      )
    );
    this.store.saveChange(actionType);
  }

  /**
   * 更新编辑器元素的时间帧
   * 优化版本：在拖拽过程中只更新状态，不触发重渲染
   * @param editorElement 要更新的元素
   * @param timeFrame 新的时间帧
   * @param isDragEnd 是否是拖拽结束的更新
   */
  updateEditorElementTimeFrame(
    editorElement: EditorElement,
    timeFrame: Partial<TimeFrame>,
    isDragEnd: boolean = false
  ) {
    const index = this.store.editorElements.findIndex(
      (element) => element.id === editorElement.id
    );
    if (index === -1) return;

    const element = this.store.editorElements[index];
    element.timeFrame = {
      ...element.timeFrame,
      ...timeFrame,
    };

    // 只在拖拽结束或非拖拽状态下执行重渲染
    if (isDragEnd || !this.store.isDraggingTimeFrame) {
      this.refreshElements();
      this.store.updateMediaElements();

      // 如果是拖拽结束，保存更改并更新最大时间
      if (isDragEnd) {
        // 更新最大时间，确保播放和indicator不超过最大endtime
        this.store.updateMaxTime();

        // 开始分组操作，将连续的时间帧调整视为一个操作
        if (!this.store.historyManager.isGrouping) {
          this.store.startHistoryGroup("元素移动");
        }
        this.store.saveChange("元素移动");

        // 延迟结束分组，允许短时间内的连续操作被视为一组
        setTimeout(() => {
          this.store.endHistoryGroup();
        }, 500);
      }
    } else {
      // 在拖拽过程中，只更新媒体元素的当前时间，不触发完整的重渲染
      this.store.updateMediaElementsLite(editorElement.id);
    }
  }

  /**
   * 添加编辑器元素
   * @param editorElement 要添加的元素
   */
  addEditorElement(editorElement: EditorElement) {
    // 添加元素到数组
    this.setEditorElements([editorElement, ...this.store.editorElements]);

    // 创建fabric对象并添加到canvas
    this.addElement(editorElement);

    // 刷新动画
    this.store.refreshAnimations();

    // 将新元素添加到适当的轨道，并获取碰撞检测结果
    this.store.trackManager.handleNewElement(editorElement);

    // 更新最大时间
    this.store.updateMaxTime();

    // 根据轨道顺序更新Canvas上的元素显示顺序
    this.store.updateCanvasOrderByTrackOrder();

    // 对于媒体元素（视频、音频、图片），跳转到元素开始时间以确保立即可见
    if (
      editorElement.type === "video" ||
      editorElement.type === "audio" ||
      editorElement.type === "image"
    ) {
      this.store.updateTimeTo(editorElement.timeFrame.start);
    } else {
      // 对于其他元素类型，更新到当前时间
      this.store.updateTimeTo(this.store.currentTimeInMs);
    }

    if (!this.canvas) return;
    const element = this.store.editorElements.find(
      (el) => el.id === editorElement.id
    );
    if (!element) return;
    this.store.setSelectedElement(element);
    this.canvas.renderAll();
    this.store.saveToHistory("元素添加");
  }

  /**
   * 移除编辑器元素
   * @param id 要移除的元素ID
   */
  removeEditorElement(id: string) {
    console.log(`Removing editor element: ${id}`);

    const elementIndex = this.store.editorElements.findIndex(
      (el) => el.id === id
    );
    if (elementIndex === -1) {
      console.warn(`Element with id ${id} not found for removal.`);
      return;
    }

    const [elementToRemove] = this.store.editorElements.splice(elementIndex, 1);
    console.log(
      `Removing element: ${elementToRemove.name}, type: ${elementToRemove.type}`
    );

    this._cleanupElementReferences(id, elementToRemove);
    this._removeElementFromDOM(elementToRemove);
    this._finalizeElementRemoval(id);
  }

  private _cleanupElementReferences(id: string, element: EditorElement) {
    // Remove animations
    this.store.animations = this.store.animations.filter(
      (animation) => animation.targetId !== id
    );

    // Remove from tracks
    this.store.trackManager.handleElementDeleted(id);

    // Remove from canvas
    if (element.fabricObject && this.canvas) {
      this.canvas.remove(element.fabricObject);
    }

    // Update elements array
    this.setEditorElements([...this.store.editorElements]);
  }

  private _removeElementFromDOM(element: EditorElement) {
    // Remove media elements from DOM
    if (this._isMediaElement(element)) {
      const mediaElementId = (element.properties as any)?.elementId;
      if (mediaElementId) {
        console.log(`Removing media element from DOM: ${mediaElementId}`);
        const mediaElement = document.getElementById(mediaElementId);
        if (mediaElement) {
          mediaElement.remove();
        }
      }
    }

    // Remove element itself from DOM
    const domElement = document.getElementById(element.id);
    if (domElement) {
      console.log("remove document", element.id);
      domElement.remove();
    }
  }

  private _isMediaElement(element: EditorElement): boolean {
    return [MEDIA_TYPES.VIDEO, MEDIA_TYPES.AUDIO, MEDIA_TYPES.IMAGE].includes(
      element.type as any
    );
  }

  private _finalizeElementRemoval(id: string) {
    // Clear selection if needed
    if (this.store.selectedElement?.id === id) {
      this.store.setSelectedElement(null);
    }

    // Refresh elements and animations
    this.refreshElements();
    this.store.refreshAnimations();

    // Save history
    this.store.saveToHistory("元素删除");

    // Update timeline
    const currentTime = this.store.currentTimeInMs;
    this.store.updateMaxTime();

    if (this.store.maxDuration > 0 && currentTime > this.store.maxDuration) {
      this.store.handleSeek(this.store.maxDuration);
    }
  }

  /**
   * 元素排序相关方法
   */
  reorderElements(
    startIndex: number,
    endIndex: number,
    placement?: "above" | "below"
  ) {
    // 根据放置位置调整目标索引
    let adjustedEndIndex = endIndex;
    if (placement === "below") {
      adjustedEndIndex = endIndex + 1;
    }

    // 临时保存要移动的元素
    const [elementToMove] = this.store.editorElements.splice(startIndex, 1);

    // 在目标位置插入元素
    this.store.editorElements.splice(adjustedEndIndex, 0, elementToMove);

    // 更新画布排序
    this.updateCanvasOrder();

    // 选中移动后的元素
    const movedElement = this.store.editorElements[adjustedEndIndex];
    if (movedElement) {
      this.store.setSelectedElement(movedElement);
    }

    // 保存更改
    this.store.saveChange();

    // 返回移动信息
    return {
      sourceIndex: startIndex,
      targetIndex: adjustedEndIndex,
      element: movedElement,
      placement: placement,
    };
  }

  /**
   * 根据拖拽垂直距离交换元素位置
   */
  swapElementsByDrag(elementId: string, deltaY: number) {
    // 获取元素当前索引
    const currentIndex = this.store.editorElements.findIndex(
      (el) => el.id === elementId
    );
    if (currentIndex === -1) return; // 元素不存在

    // 改进的敏感度计算，使拖拽更加自然
    const dragSensitivity = Math.min(0.12, 0.06 + Math.abs(deltaY) * 0.0003);
    const moveDistance =
      Math.sign(deltaY) *
      Math.min(
        Math.floor(Math.abs(deltaY) * dragSensitivity),
        this.store.editorElements.length - 1
      );

    if (moveDistance === 0) return; // 移动距离不足，不进行交换

    // 计算目标位置索引
    let targetIndex = currentIndex + moveDistance;

    // 确保目标索引在有效范围内
    targetIndex = Math.max(
      0,
      Math.min(targetIndex, this.store.editorElements.length - 1)
    );

    // 如果目标索引等于当前索引，则不需要移动
    if (targetIndex === currentIndex) return;

    // 使用现有的reorderElements方法来实现一致的元素重排序
    this.reorderElements(currentIndex, targetIndex);

    // 返回有关移动操作的信息，可用于动画
    return {
      sourceIndex: currentIndex,
      targetIndex: targetIndex,
      element: this.store.editorElements[targetIndex],
    };
  }

  /**
   * 翻转元素
   */
  flipElement(id: string, flipType: "horizontal" | "vertical") {
    const element = this.store.editorElements.find((el) => el.id === id);
    const fabricObject = element?.fabricObject;
    if (element && fabricObject) {
      if (flipType === "horizontal") {
        fabricObject.set("flipX", !element.placement.flipX);
        element.placement = {
          ...element.placement,
          flipX: !element.placement.flipX,
        };
      } else {
        fabricObject.set("flipY", !element.placement.flipY);
        element.placement = {
          ...element.placement,
          flipY: !element.placement.flipY,
        };
      }

      this.canvas.requestRenderAll();
      this.store.saveChange();
    }
  }

  /**
   * 设置媒体元素边框
   */
  setMediaElementBorder(
    id: string,
    property: keyof BorderStyle,
    value: string | number
  ) {
    const element = this.store.editorElements.find((el) => el.id === id) as
      | ImageEditorElement
      | VideoEditorElement;
    const mediaObject = element?.fabricObject;

    if (element && ("image" === element.type || "video" === element.type)) {
      element.properties.border = {
        ...element.properties.border,
        [property]: value,
      };
      //@ts-ignore
      mediaObject?.set("imageBorderColor", element.properties.border.color);
      //@ts-ignore
      mediaObject?.set("borderWidth", element.properties.border.width);
      //@ts-ignore
      mediaObject?.set("borderStyle", element.properties.border.style);
      //@ts-ignore
      mediaObject?.set("borderRadius", element.properties.border.borderRadius);
    }
    this.canvas.requestRenderAll();
    this.store.saveChange();
  }

  /**
   * 获取当前激活的元素
   */
  getActiveElement(): EditorElement | null {
    if (!this.canvas) return null;
    const activeObject = this.canvas.getActiveObject();
    if (!activeObject) return null;

    return (
      this.store.editorElements.find(
        (element) => element.fabricObject === activeObject
      ) || null
    );
  }

  /**
   * 检查是否有激活的元素
   */
  isActiveElement(): boolean {
    return this.canvas?.getActiveObject() !== null;
  }
}
